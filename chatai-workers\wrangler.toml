name = "chatai-workers"
main = "src/index.js"
compatibility_date = "2024-01-01"

# 默认环境配置
[vars]
# API密钥应通过 wrangler secret put 命令设置，不要在此文件中硬编码
# 示例: echo "your-api-keys" | wrangler secret put GEMINI_API_KEYS
ENVIRONMENT = "development"

# KV 存储 (用于聊天数据)
# 注意：请替换为你自己的 KV Namespace ID
[[kv_namespaces]]
binding = "CHAT_STORAGE"
id = "your-kv-namespace-id"
preview_id = "your-kv-namespace-id"

# R2 存储 (用于文件上传)
# 注意：请替换为你自己的 R2 Bucket 名称
[[r2_buckets]]
binding = "FILE_STORAGE"
bucket_name = "your-r2-bucket-name"
preview_bucket_name = "your-r2-bucket-name"

# Durable Objects (用于实时聊天)
[[durable_objects.bindings]]
name = "CHAT_ROOM"
class_name = "ChatRoom"

[[migrations]]
tag = "v1"
new_sqlite_classes = ["ChatRoom"]

# 路由配置
[triggers]
crons = ["0 0 * * *"]

# 资源限制
# [limits]
# cpu_ms = 50000

# 生产环境配置
[env.production]
name = "chatai-workers-production"

[env.production.vars]
ENVIRONMENT = "production"

[[env.production.kv_namespaces]]
binding = "CHAT_STORAGE"
id = "your-production-kv-namespace-id"
preview_id = "your-production-kv-namespace-id"

[[env.production.r2_buckets]]
binding = "FILE_STORAGE"
bucket_name = "your-production-r2-bucket-name"
preview_bucket_name = "your-production-r2-bucket-name"

[[env.production.durable_objects.bindings]]
name = "CHAT_ROOM"
class_name = "ChatRoom"
